#!/usr/bin/env python

"""
测试新的--dataset和--episodes参数功能
"""

import subprocess
import sys
from pathlib import Path

def test_help():
    """测试帮助信息"""
    print("=== 测试帮助信息 ===")
    try:
        result = subprocess.run([
            sys.executable, "lerobot_dual/tools/remove_episodes.py", "--help"
        ], capture_output=True, text=True, cwd="/Users/<USER>/Documents/PyCharm/MacPythonEnv")
        
        print("帮助信息:")
        print(result.stdout)
        
        # 检查是否包含新参数
        if "--dataset" in result.stdout and "--episodes" in result.stdout:
            print("✅ 新参数已正确添加到帮助信息中")
        else:
            print("❌ 新参数未在帮助信息中找到")
            
    except Exception as e:
        print(f"❌ 测试帮助信息失败: {e}")

def test_parameter_validation():
    """测试参数验证"""
    print("\n=== 测试参数验证 ===")
    
    # 测试1: 缺少必需参数
    print("1. 测试缺少episodes参数:")
    try:
        result = subprocess.run([
            sys.executable, "lerobot_dual/tools/remove_episodes.py", 
            "--dataset", "/tmp/test"
        ], capture_output=True, text=True, cwd="/Users/<USER>/Documents/PyCharm/MacPythonEnv")
        
        if "required" in result.stderr.lower() and "episodes" in result.stderr.lower():
            print("✅ 正确检测到缺少episodes参数")
        else:
            print(f"❌ 未正确检测到缺少episodes参数: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 测试2: 互斥参数
    print("\n2. 测试互斥参数 (repo-id 和 dataset):")
    try:
        result = subprocess.run([
            sys.executable, "lerobot_dual/tools/remove_episodes.py", 
            "--repo-id", "test/repo",
            "--dataset", "/tmp/test",
            "--episodes", "1,2"
        ], capture_output=True, text=True, cwd="/Users/<USER>/Documents/PyCharm/MacPythonEnv")
        
        if "not allowed" in result.stderr.lower() or "mutually exclusive" in result.stderr.lower():
            print("✅ 正确检测到互斥参数冲突")
        else:
            print(f"❌ 未正确检测到互斥参数冲突: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    # 测试3: 缺少数据集参数
    print("\n3. 测试缺少数据集参数:")
    try:
        result = subprocess.run([
            sys.executable, "lerobot_dual/tools/remove_episodes.py", 
            "--episodes", "1,2"
        ], capture_output=True, text=True, cwd="/Users/<USER>/Documents/PyCharm/MacPythonEnv")
        
        if "required" in result.stderr.lower():
            print("✅ 正确检测到缺少数据集参数")
        else:
            print(f"❌ 未正确检测到缺少数据集参数: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_episodes_parsing():
    """测试episodes参数解析"""
    print("\n=== 测试Episodes参数解析 ===")
    
    # 创建一个简单的测试脚本来验证解析逻辑
    test_script = '''
import sys
sys.path.append("/Users/<USER>/Documents/PyCharm/MacPythonEnv")
from lerobot_dual.tools.remove_episodes import _parse_episodes_list

# 测试不同的episodes格式
test_cases = [
    ("4,5", [4, 5]),
    ("1-3", [1, 2, 3]),
    ("1-3,5,7-9", [1, 2, 3, 5, 7, 8, 9]),
    ("10", [10]),
]

for input_str, expected in test_cases:
    result = _parse_episodes_list(input_str)
    if result == expected:
        print(f"✅ '{input_str}' -> {result}")
    else:
        print(f"❌ '{input_str}' -> {result}, 期望: {expected}")
'''
    
    try:
        result = subprocess.run([
            sys.executable, "-c", test_script
        ], capture_output=True, text=True)
        
        print("Episodes解析测试结果:")
        print(result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
            
    except Exception as e:
        print(f"❌ 测试episodes解析失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    examples = [
        {
            "description": "使用本地数据集路径删除episodes 4和5",
            "command": "python lerobot_dual/tools/remove_episodes.py --dataset /home/<USER>/.cache/huggingface/lerobot/fibot/so101_dual_55 --episodes 4,5"
        },
        {
            "description": "使用本地数据集路径删除episodes范围1-3和单个episode 7",
            "command": "python lerobot_dual/tools/remove_episodes.py --dataset /path/to/dataset --episodes 1-3,7"
        },
        {
            "description": "使用repo-id删除episodes（原有功能）",
            "command": "python lerobot_dual/tools/remove_episodes.py --repo-id lerobot/pusht --episodes 1-5,7,10-12"
        },
        {
            "description": "使用本地数据集并创建备份",
            "command": "python lerobot_dual/tools/remove_episodes.py --dataset /path/to/dataset --episodes 4,5 --backup"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}:")
        print(f"   {example['command']}")
        print()

if __name__ == "__main__":
    print("🧪 测试remove_episodes.py的新参数功能")
    print("=" * 50)
    
    test_help()
    test_parameter_validation()
    test_episodes_parsing()
    show_usage_examples()
    
    print("\n🎯 测试完成！")
    print("\n💡 提示: 要实际测试删除功能，请使用真实的数据集路径，例如:")
    print("python lerobot_dual/tools/remove_episodes.py --dataset /Users/<USER>/Documents/PyCharm/MacPythonEnv/lerobot_dual/tools/so101_dual_55 --episodes 4,5 --backup")
