#!/usr/bin/env python

# Copyright 2024 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
import argparse
import contextlib
import logging
import shutil
import sys
import tempfile
import time
import json
import pandas as pd
from copy import deepcopy
from pathlib import Path

from huggingface_hub import HfApi
from huggingface_hub.utils import RevisionNotFoundError

from lerobot.common.datasets.compute_stats import aggregate_stats
from lerobot.common.datasets.lerobot_dataset import LeRobotDataset, LeRobotDatasetMetadata
from lerobot.common.datasets.utils import (
    EPISODES_PATH,
    EPISODES_STATS_PATH,
    INFO_PATH,
    TASKS_PATH,
    append_jsonlines,
    create_lerobot_dataset_card,
    write_episode,
    write_episode_stats,
    write_info,
)
from lerobot.common.utils.utils import init_logging


def remove_episodes(
    dataset: LeRobotDataset,
    episodes_to_remove: list[int],
    backup: str | Path | bool = False,
) -> LeRobotDataset:
    """
    Removes specified episodes from a LeRobotDataset and updates all metadata and files accordingly.

    Args:
        dataset: The LeRobotDataset to modify
        episodes_to_remove: List of episode indices to remove
        backup: Controls backup behavior:
                   - False: No backup is created
                   - True: Create backup at default location next to dataset
                   - str/Path: Create backup at the specified location

    Returns:
        Updated LeRobotDataset with specified episodes removed
    """
    if not episodes_to_remove:
        return dataset

    if not all(ep_idx in dataset.meta.episodes for ep_idx in episodes_to_remove):
        raise ValueError("Episodes to remove must be valid episode indices in the dataset")

    # Calculate the new metadata
    new_meta = deepcopy(dataset.meta)
    new_meta.info["total_episodes"] -= len(episodes_to_remove)
    new_meta.info["total_frames"] -= sum(
        dataset.meta.episodes[ep_idx]["length"] for ep_idx in episodes_to_remove
    )

    for ep_idx in episodes_to_remove:
        new_meta.episodes.pop(ep_idx)
        new_meta.episodes_stats.pop(ep_idx)
    new_meta.stats = aggregate_stats(list(new_meta.episodes_stats.values()))

    tasks = {task for ep in new_meta.episodes.values() if "tasks" in ep for task in ep["tasks"]}
    new_meta.tasks = {new_meta.get_task_index(task): task for task in tasks}
    new_meta.task_to_task_index = {task: idx for idx, task in new_meta.tasks.items()}
    new_meta.info["total_tasks"] = len(new_meta.tasks)

    new_meta.info["total_videos"] = (
        (new_meta.info["total_episodes"]) * len(dataset.meta.video_keys) if dataset.meta.video_keys else 0
    )

    if "splits" in new_meta.info:
        new_meta.info["splits"] = {"train": f"0:{new_meta.info['total_episodes']}"}

    # Now that the metadata is recalculated, we update the dataset files by
    # removing the files related to the specified episodes. We perform a safe
    # update such that if an error occurs, any changes are rolled back and the
    # dataset files are left in its original state. Optionally, a non-temporary
    # full backup can be made so that we also have the dataset in its original state.
    if backup:
        backup_path = (
            Path(backup)
            if isinstance(backup, (str, Path))
            else dataset.root.parent / f"{dataset.root.name}_backup_{int(time.time())}"
        )
        _backup_folder(dataset.root, backup_path)

    _update_dataset_files(
        new_meta,
        episodes_to_remove,
    )

    # 直接在原有dataset对象上进行修改，避免重新创建LeRobotDataset
    # 更新dataset的metadata
    dataset.meta = new_meta

    # 执行重新编号操作
    renumber_episodes_and_files(dataset)

    return dataset

def renumber_episodes_and_files(dataset: LeRobotDataset):

    root = dataset.root
    meta = dataset.meta

    # 1. Build mapping from old to new episode indices
    old_indices = sorted(meta.episodes.keys())
    new_indices = list(range(0, len(old_indices)))
    index_map = {old_idx: new_idx for old_idx, new_idx in zip(old_indices, new_indices)}

    # 2. Update parquet files content and rename them

    # Calculate cumulative frame counts for global index adjustment
    cumulative_frames = 0
    frame_offset_map = {}

    for new_idx in new_indices:
        frame_offset_map[new_idx] = cumulative_frames
        # Find the old episode index that maps to this new index
        old_idx = None
        for old, new in index_map.items():
            if new == new_idx:
                old_idx = old
                break
        if old_idx is not None:
            cumulative_frames += meta.episodes[old_idx]["length"]

    for old_idx, new_idx in index_map.items():
        old_path = root / meta.get_data_file_path(old_idx)
        new_path = root / f"data/chunk-000/episode_{new_idx:06d}.parquet"

        if old_path.exists():
            # Read the parquet file
            df = pd.read_parquet(old_path)

            # Update episode_index in the dataframe
            df['episode_index'] = new_idx

            # Update global index to be continuous across all episodes
            frame_offset = frame_offset_map[new_idx]
            df['index'] = df['frame_index'] + frame_offset

            # Create temporary file to avoid conflicts during renaming
            with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as tmp_file:
                df.to_parquet(tmp_file.name, index=False)
                tmp_path = tmp_file.name

            # Remove old file and move temp file to new location
            old_path.unlink()
            new_path.parent.mkdir(parents=True, exist_ok=True)
            import shutil
            shutil.move(tmp_path, new_path)

    # 3. Rename video files
    for old_idx, new_idx in index_map.items():
        for vid_key in meta.video_keys:
            old_vid_path = root / meta.get_video_file_path(old_idx, vid_key)
            new_vid_path = root / f"videos/chunk-000/{vid_key}/episode_{new_idx:06d}.mp4"
            if old_vid_path.exists():
                new_vid_path.parent.mkdir(parents=True, exist_ok=True)
                old_vid_path.rename(new_vid_path)

    # 4. Update episodes.jsonl
    episodes_jsonl_path = root / "meta/episodes.jsonl"
    episodes = []
    with open(episodes_jsonl_path, "r") as f:
        for line in f:
            ep = json.loads(line)
            old_idx = ep["episode_index"]
            if old_idx in index_map:
                ep["episode_index"] = index_map[old_idx]
                episodes.append(ep)
    with open(episodes_jsonl_path, "w") as f:
        for ep in sorted(episodes, key=lambda x: x["episode_index"]):
            f.write(json.dumps(ep) + "\n")

    # 5. Update episodes_stats.jsonl
    stats_jsonl_path = root / "meta/episodes_stats.jsonl"
    stats = []
    with open(stats_jsonl_path, "r") as f:
        for line in f:
            stat = json.loads(line)
            old_idx = stat["episode_index"]
            if old_idx in index_map:
                new_idx = index_map[old_idx]
                stat["episode_index"] = new_idx
                stat["stats"]["episode_index"]["min"] = [new_idx]
                stat["stats"]["episode_index"]["max"] = [new_idx]
                stat["stats"]["episode_index"]["mean"] = [float(new_idx)]
                stats.append(stat)
    with open(stats_jsonl_path, "w") as f:
        for stat in sorted(stats, key=lambda x: x["episode_index"]):
            f.write(json.dumps(stat) + "\n")

    # 6. Update metadata
    new_episodes = {}
    new_episodes_stats = {}
    for old_idx, new_idx in index_map.items():
        ep = meta.episodes[old_idx]
        ep["episode_index"] = new_idx
        new_episodes[new_idx] = ep
        stat = meta.episodes_stats[old_idx]
        new_episodes_stats[new_idx] = stat
    meta.episodes = new_episodes
    meta.episodes_stats = new_episodes_stats

    # 7. Update tasks.jsonl (重新生成task索引)
    tasks_jsonl_path = root / "meta/tasks.jsonl"

    # 收集所有剩余episodes中的tasks
    all_tasks = set()
    for ep in meta.episodes.values():
        if "tasks" in ep:
            all_tasks.update(ep["tasks"])

    # 重新生成tasks.jsonl
    with open(tasks_jsonl_path, "w") as f:
        for task_idx, task in enumerate(sorted(all_tasks)):
            f.write(json.dumps({"task_index": task_idx, "task": task}) + "\n")

    # 8. Update info.json
    info_path = root / "meta/info.json"
    with open(info_path, "r") as f:
        info = json.load(f)
    info["total_episodes"] = len(new_indices)
    info["total_tasks"] = len(all_tasks)
    # 重新计算total_frames
    total_frames = sum(ep["length"] for ep in meta.episodes.values())
    info["total_frames"] = total_frames
    # 更新splits
    info["splits"] = {"train": f"0:{len(new_indices)}"}
    with open(info_path, "w") as f:
        json.dump(info, f, indent=2)

    logging.info("Renumbering complete")


def _move_file(src: Path, dest: Path) -> None:
    dest.parent.mkdir(parents=True, exist_ok=True)
    shutil.move(src, dest)


def _update_dataset_files(new_meta: LeRobotDatasetMetadata, episodes_to_remove: list[int]):
    """Update dataset files.

    This function performs a safe update for dataset files. It moves modified or removed
    episode files to a temporary directory. Once all changes are made, the temporary
    directory is deleted. If an error occurs during the update, all changes are rolled
    back and the original dataset files are restored.

    Args:
         new_meta (LeRobotDatasetMetadata): Updated metadata object containing the new
              dataset state after removing episodes
         episodes_to_remove (list[int]): List of episode indices to remove from the dataset

    Raises:
         Exception: If any operation fails, rolls back all changes and re-raises the original exception
    """
    with tempfile.TemporaryDirectory(prefix="lerobot_backup_temp_") as backup_path:
        backup_dir = Path(backup_path)

        # Init empty containers s.t. they are guaranteed to exist in the except block
        metadata_files = {}
        rel_data_paths = []
        rel_video_paths = []

        try:
            # Step 1: Update metadata files
            metadata_files = {
                INFO_PATH: lambda: write_info(new_meta.info, new_meta.root),
                EPISODES_PATH: lambda: [
                    write_episode(ep, new_meta.root) for ep in new_meta.episodes.values()
                ],
                TASKS_PATH: lambda: [
                    append_jsonlines({"task_index": idx, "task": task}, new_meta.root / TASKS_PATH)
                    for idx, task in new_meta.tasks.items()
                ],
                EPISODES_STATS_PATH: lambda: [
                    write_episode_stats(idx, stats, new_meta.root)
                    for idx, stats in new_meta.episodes_stats.items()
                ],
            }
            for file_path, update_func in metadata_files.items():
                _move_file(new_meta.root / file_path, backup_dir / file_path)
                update_func()

            # Step 2: Update data and video
            rel_data_paths = [new_meta.get_data_file_path(ep_idx) for ep_idx in episodes_to_remove]
            rel_video_paths = [
                new_meta.get_video_file_path(ep_idx, vid_key)
                for ep_idx in episodes_to_remove
                for vid_key in new_meta.video_keys
            ]
            for rel_path in rel_data_paths + rel_video_paths:
                if (new_meta.root / rel_path).exists():
                    _move_file(new_meta.root / rel_path, backup_dir / rel_path)

        except Exception as e:
            logging.error(f"Error updating dataset files: {str(e)}. Rolling back changes.")

            # Restore metadata files
            for file_path in metadata_files:
                if (backup_dir / file_path).exists():
                    _move_file(backup_dir / file_path, new_meta.root / file_path)

            # Restore data and video files
            for rel_file_path in rel_data_paths + rel_video_paths:
                if (backup_dir / rel_file_path).exists():
                    _move_file(backup_dir / rel_file_path, new_meta.root / rel_file_path)

            raise e


def _backup_folder(target_dir: Path, backup_path: Path) -> None:
    if backup_path.resolve() == target_dir.resolve() or backup_path.resolve().is_relative_to(
        target_dir.resolve()
    ):
        raise ValueError(
            f"Backup directory '{backup_path}' cannot be inside the dataset "
            f"directory '{target_dir}' as this would cause infinite recursion"
        )

    backup_path.parent.mkdir(parents=True, exist_ok=True)
    logging.info(f"Creating backup at: {backup_path}")
    shutil.copytree(target_dir, backup_path)


def _load_local_dataset_safely(repo_id: str, root_path: Path) -> LeRobotDataset:
    """
    安全地加载本地数据集，避免从HuggingFace Hub下载
    """
    from datasets import load_dataset
    from lerobot.common.datasets.utils import (
        load_info, load_episodes, load_tasks, load_episodes_stats, load_stats,
        get_episode_data_index, hf_transform_to_torch
    )
    from lerobot.common.datasets.lerobot_dataset import CODEBASE_VERSION
    from lerobot.common.datasets.video_utils import get_safe_default_codec

    # 创建一个LeRobotDataset对象，但绕过网络检查
    dataset = LeRobotDataset.__new__(LeRobotDataset)

    # 手动设置基本属性
    dataset.repo_id = repo_id
    dataset.root = root_path
    dataset.image_transforms = None
    dataset.delta_timestamps = None
    dataset.episodes = None
    dataset.tolerance_s = 1e-4
    dataset.revision = CODEBASE_VERSION
    dataset.video_backend = get_safe_default_codec()
    dataset.delta_indices = None
    dataset.image_writer = None
    dataset.episode_buffer = None

    # 手动创建metadata对象，避免网络调用
    meta = LeRobotDatasetMetadata.__new__(LeRobotDatasetMetadata)
    meta.repo_id = repo_id
    meta.root = root_path
    meta.revision = CODEBASE_VERSION

    # 直接加载本地metadata文件
    meta.info = load_info(root_path)
    meta.tasks, meta.task_to_task_index = load_tasks(root_path)
    meta.episodes = load_episodes(root_path)

    # 尝试加载episodes_stats，如果不存在则使用空字典
    try:
        meta.episodes_stats = load_episodes_stats(root_path)
    except FileNotFoundError:
        meta.episodes_stats = {}

    # 尝试加载stats，如果不存在则使用空字典
    try:
        meta.stats = load_stats(root_path)
    except FileNotFoundError:
        meta.stats = {}

    dataset.meta = meta

    # 加载HuggingFace数据集
    data_path = str(root_path / "data")
    hf_dataset = load_dataset("parquet", data_dir=data_path, split="train")
    hf_dataset.set_transform(hf_transform_to_torch)
    dataset.hf_dataset = hf_dataset

    # 设置episode数据索引
    dataset.episode_data_index = get_episode_data_index(meta.episodes, None)

    return dataset


def _parse_episodes_list(episodes_str: str) -> list[int]:
    """
    Parse a string of episode indices, ranges, and comma-separated lists into a list of integers.
    """
    episodes = []
    for ep in episodes_str.split(","):
        if "-" in ep:
            start, end = ep.split("-")
            episodes.extend(range(int(start), int(end) + 1))
        else:
            episodes.append(int(ep))
    return episodes


def _delete_hub_file(hub_api: HfApi, repo_id: str, file_path: str, branch: str):
    try:
        with contextlib.suppress(RevisionNotFoundError):
            if hub_api.file_exists(
                repo_id,
                file_path,
                repo_type="dataset",
                revision=branch,
            ):
                hub_api.delete_file(
                    path_in_repo=file_path,
                    repo_id=repo_id,
                    repo_type="dataset",
                    revision=branch,
                )
                logging.info(f"Deleted '{file_path}' from branch '{branch}'")
    except Exception as e:
        logging.error(f"Error removing file '{file_path}' from the hub: {str(e)}")


def _remove_episodes_from_hub(
    updated_dataset: LeRobotDataset, episodes_to_remove: list[int], branch: str | None = None
):
    """Remove episodes from the hub repository at a specific revision."""
    hub_api = HfApi()

    try:
        for ep_idx in episodes_to_remove:
            data_path = str(updated_dataset.meta.get_data_file_path(ep_idx))
            _delete_hub_file(hub_api, updated_dataset.repo_id, data_path, branch)

            for vid_key in updated_dataset.meta.video_keys:
                video_path = str(updated_dataset.meta.get_video_file_path(ep_idx, vid_key))
                _delete_hub_file(hub_api, updated_dataset.repo_id, video_path, branch)

        logging.info(f"Successfully removed episode files from Hub on branch '{branch}'")

    except RevisionNotFoundError:
        logging.error(f"Branch '{branch}' not found in repository '{updated_dataset.repo_id}'")
    except Exception as e:
        logging.error(f"Error during Hub operations: {str(e)}")


def main():
    parser = argparse.ArgumentParser(description="Remove episodes from a LeRobot dataset")

    # 创建互斥组：要么使用repo-id+root，要么使用dataset路径
    dataset_group = parser.add_mutually_exclusive_group(required=True)
    dataset_group.add_argument(
        "--repo-id",
        type=str,
        help="Name of hugging face repository containing a LeRobotDataset dataset (e.g. `lerobot/pusht`).",
    )
    dataset_group.add_argument(
        "--dataset",
        type=Path,
        help="Absolute path to the dataset directory (e.g., '/home/<USER>/.cache/huggingface/lerobot/fibot/so101_dual_55').",
    )

    parser.add_argument(
        "--root",
        type=Path,
        default=None,
        help="Root directory for the dataset stored locally. Only used with --repo-id. By default, the dataset will be loaded from hugging face cache folder, or downloaded from the hub if available.",
    )
    parser.add_argument(
        "-e",
        "--episodes",
        type=str,
        required=True,
        help="Episodes to remove. Can be a single index, comma-separated indices, or ranges (e.g., '4,5' or '1-5,7,10-12')",
    )
    parser.add_argument(
        "-b",
        "--backup",
        nargs="?",
        const=True,
        default=False,
        help="Create a backup before modifying the dataset. Without a value, creates a backup in the default location. "
        "With a value, either 'true'/'false' or a path to store the backup.",
    )
    parser.add_argument(
        "--push-to-hub",
        type=int,
        default=1,
        help="Upload to Hugging Face hub.",
    )
    parser.add_argument(
        "--private",
        type=int,
        default=0,
        help="If set, the repository on the Hub will be private",
    )
    parser.add_argument(
        "--tags",
        type=str,
        nargs="+",
        help="List of tags to apply to the dataset on the Hub",
    )
    parser.add_argument("--license", type=str, default=None, help="License to use for the dataset on the Hub")
    args = parser.parse_args()

    # Parse the backup argument
    backup_value = args.backup
    if isinstance(backup_value, str):
        if backup_value.lower() == "true":
            backup_value = True
        elif backup_value.lower() == "false":
            backup_value = False
        # Otherwise, it's treated as a path

    # Parse episodes to remove
    episodes_to_remove = _parse_episodes_list(args.episodes)
    if not episodes_to_remove:
        logging.warning("No episodes specified to remove")
        sys.exit(0)

    # Load the dataset
    if args.dataset:
        # 使用本地数据集路径
        logging.info(f"Loading dataset from local path: {args.dataset}")
        if not args.dataset.exists():
            logging.error(f"Dataset path does not exist: {args.dataset}")
            sys.exit(1)

        # 检查必要的文件是否存在
        meta_dir = args.dataset / "meta"
        if not meta_dir.exists():
            logging.error(f"Meta directory not found: {meta_dir}")
            sys.exit(1)

        required_files = ["info.json", "episodes.jsonl", "tasks.jsonl"]
        for file_name in required_files:
            file_path = meta_dir / file_name
            if not file_path.exists():
                logging.error(f"Required metadata file not found: {file_path}")
                sys.exit(1)

        # 从路径推断repo_id（用于显示）
        repo_id = args.dataset.name  # 使用目录名作为repo_id

        try:
            # 创建一个本地专用的数据集加载方法
            dataset = _load_local_dataset_safely(repo_id, args.dataset)
        except Exception as e:
            logging.error(f"Failed to load local dataset: {e}")
            logging.error("Make sure the dataset directory contains all required files and is properly formatted.")
            sys.exit(1)

        # 本地数据集不需要推送到hub
        push_to_hub_enabled = False
        target_revision_tag = None
        push_branch = None

        logging.info(f"Local dataset loaded: {repo_id}")
    else:
        # 使用repo-id加载
        logging.info(f"Loading dataset from repository: {args.repo_id}")
        dataset = LeRobotDataset(repo_id=args.repo_id, root=args.root, delta_timestamps=None)

        target_revision_tag = dataset.revision
        push_branch = "main"
        push_to_hub_enabled = bool(args.push_to_hub)

        logging.info(
            f"Dataset loaded using revision tag: {target_revision_tag}. Changes will be pushed to 'main' and this tag will be updated."
        )

    logging.info(f"Dataset has {dataset.meta.total_episodes} episodes")

    # Modify the dataset
    logging.info(f"Removing {len(set(episodes_to_remove))} episodes: {sorted(set(episodes_to_remove))}")
    updated_dataset = remove_episodes(
        dataset=dataset,
        episodes_to_remove=episodes_to_remove,
        backup=backup_value,
    )
    logging.info(
        f"Successfully removed episodes. Dataset now has {updated_dataset.meta.total_episodes} episodes."
    )

    # 只有在使用repo-id且启用push-to-hub时才推送
    if push_to_hub_enabled and args.push_to_hub:
        logging.info("Pushing dataset to hub...")

        updated_dataset.push_to_hub(
            tags=args.tags,
            private=bool(args.private),
            license=args.license,
            branch=push_branch,
            tag_version=False,  # Disable automatic tagging here, we'll do it manually later
        )
        updated_card = create_lerobot_dataset_card(
            tags=args.tags, dataset_info=updated_dataset.meta.info, license=args.license
        )
        updated_card.push_to_hub(repo_id=updated_dataset.repo_id, repo_type="dataset", revision=push_branch)
        _remove_episodes_from_hub(updated_dataset, episodes_to_remove, branch=push_branch)

        logging.info(
            f"Updating tag '{target_revision_tag}' to point to the latest commit on branch '{push_branch}'..."
        )
        hub_api = HfApi()
        try:
            # Delete the old tag first if it exists
            with contextlib.suppress(RevisionNotFoundError):
                hub_api.delete_tag(updated_dataset.repo_id, tag=target_revision_tag, repo_type="dataset")
                logging.info(f"Deleted existing tag '{target_revision_tag}'.")

            # Create the new tag pointing to the head of the push branch
            hub_api.create_tag(
                updated_dataset.repo_id,
                tag=target_revision_tag,
                revision=push_branch,
                repo_type="dataset",
            )
            logging.info(
                f"Successfully created tag '{target_revision_tag}' pointing to branch '{push_branch}'."
            )

        except Exception as e:
            logging.error(f"Error during tag update for '{target_revision_tag}': {str(e)}")

        logging.info("Dataset pushed to hub.")
    elif args.dataset:
        logging.info(f"Local dataset processing complete. Updated dataset saved at: {args.dataset}")
    else:
        logging.info("Dataset processing complete. No hub operations performed.")


if __name__ == "__main__":
    init_logging()
    main()
