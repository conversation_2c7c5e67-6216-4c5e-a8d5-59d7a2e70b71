# Remove Episodes Tool - 使用说明

这个工具用于从LeRobot数据集中删除指定的episodes，支持本地数据集和HuggingFace Hub数据集。

## 📋 参数说明

### 必需参数（二选一）
- `--repo-id REPO_ID`: HuggingFace仓库ID（如 `lerobot/pusht`）
- `--dataset DATASET`: 本地数据集的绝对路径

### 必需参数
- `--episodes EPISODES`: 要删除的episodes，支持多种格式：
  - 单个episode: `4`
  - 多个episodes: `4,5,8`
  - 范围: `1-5` (删除1,2,3,4,5)
  - 混合: `1-3,5,7-9` (删除1,2,3,5,7,8,9)

### 可选参数
- `--root ROOT`: 本地根目录（仅与`--repo-id`一起使用）
- `--backup [BACKUP]`: 创建备份
- `--push-to-hub`: 推送到HuggingFace Hub（仅与`--repo-id`一起使用）
- `--private`: 设置仓库为私有
- `--tags`: 添加标签
- `--license`: 设置许可证

## 💡 使用示例

### 1. 使用本地数据集路径

```bash
# 删除episodes 4和5
python lerobot_dual/tools/remove_episodes.py \
    --dataset /home/<USER>/.cache/huggingface/lerobot/fibot/so101_dual_55 \
    --episodes 4,5

# 删除episodes范围并创建备份
python lerobot_dual/tools/remove_episodes.py \
    --dataset /path/to/your/dataset \
    --episodes 1-3,7 \
    --backup

# 删除单个episode
python lerobot_dual/tools/remove_episodes.py \
    --dataset /path/to/your/dataset \
    --episodes 10
```

### 2. 使用HuggingFace仓库ID

```bash
# 从HuggingFace Hub删除episodes
python lerobot_dual/tools/remove_episodes.py \
    --repo-id lerobot/pusht \
    --episodes 1-5,7,10-12 \
    --push-to-hub 1

# 使用本地缓存
python lerobot_dual/tools/remove_episodes.py \
    --repo-id your/dataset \
    --root /path/to/local/cache \
    --episodes 4,5
```

## 🔧 功能特性

这个工具提供以下核心功能：

1. **完整的数据一致性**: 更新parquet文件内容的`episode_index`和`index`字段，不仅仅是重命名文件
2. **智能任务管理**: 删除episodes后自动重新生成`tasks.jsonl`文件
3. **全面的元数据更新**: 自动更新`info.json`中的所有统计信息
4. **连续索引保证**: 确保全局`index`字段在所有episodes中保持连续性

## ⚠️ 注意事项

1. **备份建议**: 在删除episodes前建议使用`--backup`参数创建备份
2. **路径格式**: `--dataset`参数需要提供绝对路径
3. **权限要求**: 确保对数据集目录有读写权限
4. **数据一致性**: 工具会自动检查并修复所有相关文件的一致性

## 🧪 验证工具

运行数据集一致性检查：

```bash
python lerobot_dual/tools/comprehensive_dataset_check.py
```

这个检查工具会验证：
- 文件数量一致性
- Episode索引连续性
- Parquet文件内容一致性
- Index字段连续性
- Tasks一致性

## 📊 处理的文件类型

工具会自动处理以下文件类型：

- **data/*.parquet**: 更新文件名和内容（episode_index、index字段）
- **videos/*.mp4**: 重命名视频文件
- **meta/episodes.jsonl**: 更新episode信息
- **meta/episodes_stats.jsonl**: 更新统计信息
- **meta/tasks.jsonl**: 重新生成任务索引
- **meta/info.json**: 更新所有统计信息

## 🎯 工作流程

1. **加载数据集**: 从本地路径或HuggingFace Hub加载数据集
2. **验证输入**: 检查要删除的episodes是否存在
3. **创建备份**: 如果指定了`--backup`参数，创建完整备份
4. **删除文件**: 移除指定episodes的parquet和视频文件
5. **重新编号**: 重命名剩余文件并更新内容
6. **更新元数据**: 重新生成所有元数据文件
7. **验证一致性**: 确保所有文件保持一致

## 📈 示例输出

```
INFO Loading dataset from local path: /path/to/dataset
INFO Dataset has 55 episodes
INFO Removing 44 episodes: [0, 1, 2, ..., 43]
INFO Creating backup at: /path/to/dataset_backup_xxx
INFO Renumbering complete
INFO Successfully removed episodes. Dataset now has 10 episodes.
INFO Local dataset processing complete.
```
